/**
 * Simple test to verify the new AI parsing logic works
 */

// Mock video data
const mockVideos = [
  { id: '1', title: 'Video 1' },
  { id: '2', title: 'Video 2' },
  { id: '3', title: 'Video 3' },
  { id: '4', title: 'Video 4' },
  { id: '5', title: 'Video 5' },
]

function parseAIResponse(responseText, filteredVideos, count) {
  const lines = responseText.trim().split('\n').filter(line => line.trim())
  
  if (lines.length === 0) {
    throw new Error('Empty AI response')
  }

  const firstLine = lines[0].trim()
  let selectedIndices = []
  
  // Strategy 1: Direct comma-separated parsing
  try {
    // Remove common prefixes like "Selected:", "Indices:", etc.
    const cleanLine = firstLine.replace(/^(selected|indices|chosen|picked):\s*/i, '')
    selectedIndices = cleanLine
      .split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0)
      .map(s => parseInt(s, 10))
      .filter(n => !isNaN(n) && n >= 0 && n < filteredVideos.length)
  } catch (e) {
    console.log('Strategy 1 failed')
  }

  // Strategy 2: Extract numbers from text
  if (selectedIndices.length === 0) {
    try {
      const numberMatches = firstLine.match(/\d+/g)
      console.log('   Strategy 2 - Number matches:', numberMatches)
      if (numberMatches) {
        const parsed = numberMatches.map(s => parseInt(s, 10))
        console.log('   Strategy 2 - Parsed numbers:', parsed)
        selectedIndices = parsed
          .filter(n => {
            const isValid = !isNaN(n) && n >= 0 && n < filteredVideos.length
            console.log(`   Strategy 2 - Number ${n}: isNaN=${isNaN(n)}, >=0=${n >= 0}, <length=${n < filteredVideos.length}, valid=${isValid}`)
            return isValid
          })
          .slice(0, count)
        console.log('   Strategy 2 - Selected indices:', selectedIndices)
      }
    } catch (e) {
      console.log('Strategy 2 failed')
    }
  }

  // Strategy 3: JSON fallback
  if (selectedIndices.length === 0) {
    try {
      const jsonMatch = responseText.match(/\[[\d,\s]+\]/)
      if (jsonMatch) {
        const arrayStr = jsonMatch[0]
        console.log('   JSON match found:', arrayStr)
        const parsed = JSON.parse(arrayStr)
        console.log('   Parsed JSON:', parsed)
        if (Array.isArray(parsed)) {
          selectedIndices = parsed
            .filter(n => typeof n === 'number' && n >= 0 && n < filteredVideos.length)
          console.log('   After filtering:', selectedIndices)
        }
      }
    } catch (e) {
      console.log('Strategy 3 failed:', e.message)
    }
  }

  // Limit to requested count
  selectedIndices = selectedIndices.slice(0, count)

  // Extract explanation
  let explanation = `Selected ${selectedIndices.length} best videos.`
  if (lines.length > 1) {
    explanation = lines.slice(1).join(' ').trim()
  }

  return {
    indices: selectedIndices,
    explanation
  }
}

// Test cases
const testCases = [
  {
    name: 'Simple comma-separated',
    response: '0, 2, 4\nThese videos are the best.',
    expected: [0, 2, 4]
  },
  {
    name: 'With extra text',
    response: 'Selected: 1, 3\nGood quality videos.',
    expected: [1, 3]
  },
  {
    name: 'JSON fallback',
    response: '[0, 2, 4]\nFallback format.',
    expected: [0, 2, 4]
  }
]

console.log('🧪 Testing AI Response Parsing\n')

testCases.forEach(testCase => {
  console.log(`📝 ${testCase.name}`)
  console.log(`   Input: "${testCase.response.replace('\n', '\\n')}"`)
  
  try {
    const result = parseAIResponse(testCase.response, mockVideos, 5)
    console.log(`   Parsed: [${result.indices.join(', ')}]`)
    console.log(`   Expected: [${testCase.expected.join(', ')}]`)
    console.log(`   ✅ Success: ${JSON.stringify(result.indices) === JSON.stringify(testCase.expected)}`)
    console.log(`   Explanation: "${result.explanation}"`)
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`)
  }
  console.log('')
})

console.log('✅ All tests completed!')

/**
 * Test file to demonstrate the improved AI response parsing
 * This shows how the new text-based format is more reliable than JSON
 */

// Mock video data for testing
const mockVideos = [
  { id: '1', title: 'Funny Cat Video 1' },
  { id: '2', title: 'Funny Cat Video 2' },
  { id: '3', title: 'Funny Cat Video 3' },
  { id: '4', title: 'Funny Cat Video 4' },
  { id: '5', title: 'Funny Cat Video 5' },
]

/**
 * Test the new text-based parsing logic
 */
function testTextBasedParsing() {
  console.log('🧪 Testing new text-based AI response parsing...\n')

  // Test cases that would work with the new format
  const testCases = [
    {
      name: 'Simple comma-separated format',
      response: '0, 2, 4\nThese videos have the funniest cat moments.',
      expected: [0, 2, 4]
    },
    {
      name: 'With extra spaces',
      response: '1,   3,  4  \nSelected for high engagement and humor.',
      expected: [1, 3, 4]
    },
    {
      name: 'With text mixed in',
      response: 'Selected indices: 0, 1, 3\nBest videos for funny cats content.',
      expected: [0, 1, 3]
    },
    {
      name: 'Numbers in explanation',
      response: '2, 4\nThese 2 videos have over 1 million views each.',
      expected: [2, 4]
    },
    {
      name: 'Fallback JSON format',
      response: '[1, 2, 3]\nFallback to old JSON format.',
      expected: [1, 2, 3]
    }
  ]

  testCases.forEach(testCase => {
    console.log(`📝 Testing: ${testCase.name}`)
    console.log(`   Input: "${testCase.response.replace('\n', '\\n')}"`)
    
    const result = parseAIResponse(testCase.response, mockVideos, 5)
    console.log(`   Parsed indices: [${result.indices.join(', ')}]`)
    console.log(`   Expected: [${testCase.expected.join(', ')}]`)
    console.log(`   ✅ Match: ${JSON.stringify(result.indices) === JSON.stringify(testCase.expected)}`)
    console.log(`   Explanation: "${result.explanation}"\n`)
  })
}

/**
 * Simulate the new parsing logic from ai.ts
 */
function parseAIResponse(responseText: string, filteredVideos: any[], count: number) {
  const lines = responseText.trim().split('\n').filter(line => line.trim())
  
  if (lines.length === 0) {
    throw new Error('Empty AI response')
  }

  const firstLine = lines[0].trim()
  let selectedIndices: number[] = []
  
  // Strategy 1: Direct comma-separated parsing
  try {
    selectedIndices = firstLine
      .split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0)
      .map(s => parseInt(s, 10))
      .filter(n => !isNaN(n) && n >= 0 && n < filteredVideos.length)
  } catch (e) {
    // Continue to strategy 2
  }

  // Strategy 2: Extract numbers from text
  if (selectedIndices.length === 0) {
    try {
      const numberMatches = firstLine.match(/\d+/g)
      if (numberMatches) {
        selectedIndices = numberMatches
          .map(s => parseInt(s, 10))
          .filter(n => !isNaN(n) && n >= 0 && n < filteredVideos.length)
          .slice(0, count)
      }
    } catch (e) {
      // Continue to strategy 3
    }
  }

  // Strategy 3: JSON fallback
  if (selectedIndices.length === 0) {
    try {
      const jsonMatch = responseText.match(/\[[\d,\s]+\]/)
      if (jsonMatch) {
        const arrayStr = jsonMatch[0]
        selectedIndices = JSON.parse(arrayStr)
          .filter((n: any) => typeof n === 'number' && n >= 0 && n < filteredVideos.length)
      }
    } catch (e) {
      // All strategies failed
    }
  }

  // Limit to requested count
  selectedIndices = selectedIndices.slice(0, count)

  // Extract explanation
  let explanation = `Selected ${selectedIndices.length} best videos.`
  if (lines.length > 1) {
    explanation = lines.slice(1).join(' ').trim()
    explanation = explanation
      .replace(/^(explanation|reason|because):\s*/i, '')
      .replace(/^these videos were selected/i, 'Selected')
  }

  return {
    indices: selectedIndices,
    explanation
  }
}

// Run the tests
if (typeof window === 'undefined') {
  // Only run in Node.js environment (not in browser)
  testTextBasedParsing()
}

export { testTextBasedParsing, parseAIResponse }

// Firebase AI Logic service for generating video recommendations

import { getAI, getGenerativeModel, GoogleAIBackend } from 'firebase/ai'
import { getFirebaseApp } from '@/lib/firebase/config'
import { VideoSearchResult } from '@/lib/types/video'
import { youtubeService } from './youtube'

export interface MagicQueueRequest {
  prompt: string
  count?: number
  maxDuration?: number // in minutes, optional filter
  onProgress?: (step: string, progress: number, total: number) => void
}

export interface MagicQueueResponse {
  videos: VideoSearchResult[]
  explanation?: string
  searchQueries?: string[] // The queries that were used
}

export class AIService {
  private getAI() {
    const app = getFirebaseApp()
    if (!app) {
      throw new Error('Firebase not initialized. AI service will not work.')
    }
    
    try {
      return getAI(app, { backend: new GoogleAIBackend() })
    } catch (error) {
      console.error('❌ Error initializing Firebase AI:', error)
      throw new Error('Firebase AI not configured properly. Please check your Firebase AI Logic setup.')
    }
  }

  private getModel() {
    const ai = this.getAI()
    return getGenerativeModel(ai, { model: 'gemini-2.5-flash' })
  }

  /**
   * Generate search queries based on user prompt
   */
  private async generateSearchQueries(prompt: string, count: number): Promise<string[]> {
    const model = this.getModel()

    // OPTIMIZATION: Reduce number of search queries to save quota
    // Changed from count/2 to Math.min(3, Math.ceil(count/3)) to limit API calls
    const queryCount = Math.min(3, Math.ceil(count / 3))

    const systemPrompt = `You are a YouTube search expert. Based on the user's request, generate ${queryCount} different search queries that would find the best videos for their needs.

IMPORTANT: Respond with one search query per line, no other formatting:

Guidelines:
- Create diverse search queries that cover different aspects of the request
- Use terms that are commonly used on YouTube
- Include both broad and specific queries
- Focus on findable, popular content
- Avoid overly complex or niche terms
- Prioritize quality over quantity - fewer, better queries

User request: "${prompt}"`

    const result = await model.generateContent(systemPrompt)
    const responseText = result.response.text()

    try {
      // Parse simple line-based response format
      const queries = responseText
        .trim()
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .filter(line => !line.startsWith('//') && !line.startsWith('#')) // Remove comments
        .slice(0, queryCount) // Limit to requested count

      if (queries.length === 0) {
        throw new Error('No valid search queries found')
      }

      console.log('✅ Parsed search queries:', queries)
      return queries
    } catch (parseError) {
      console.error('❌ Failed to parse search queries:', parseError)
      console.error('❌ Raw AI response:', responseText)
      // Fallback: create basic search queries from the prompt
      return [prompt, `${prompt} tutorial`, `${prompt} guide`]
    }
  }

  /**
   * Select best videos from search results using AI
   */
  private async selectBestVideos(
    prompt: string,
    allVideos: VideoSearchResult[],
    count: number,
    maxDuration?: number
  ): Promise<{ videos: VideoSearchResult[], explanation: string }> {
    const model = this.getModel()

    // Filter by duration if specified
    let filteredVideos = allVideos
    if (maxDuration) {
      filteredVideos = allVideos.filter(video => {
        const duration = this.parseDurationToMinutes(video.duration)
        return duration <= maxDuration
      })
    }

    // If we have fewer videos than requested, just return all
    if (filteredVideos.length <= count) {
      return {
        videos: filteredVideos,
        explanation: `Found ${filteredVideos.length} videos matching your criteria.`
      }
    }

    // OPTIMIZATION: If we have too many videos, pre-filter to top candidates
    // This reduces AI processing time significantly
    if (filteredVideos.length > count * 3) {
      // Sort by view count and take top candidates for AI analysis
      filteredVideos = filteredVideos
        .sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
        .slice(0, count * 3) // Analyze only 3x the requested count
    }

    // OPTIMIZATION: Prepare minimal video data for faster AI processing
    const videoData = filteredVideos.map((video, index) => ({
      index,
      title: video.title,
      channel: video.channel.title,
      description: video.description.substring(0, 100), // Reduced from 200 to 100
      duration: video.duration,
      views: this.formatViewCount(video.viewCount || 0)
    }))

    // OPTIMIZATION: Simplified text-based prompt for maximum reliability and speed
    const systemPrompt = `Select ${count} best videos for: "${prompt}"

Videos:
${JSON.stringify(videoData, null, 1)}

IMPORTANT: Respond with ONLY the selected video indices as a comma-separated list, followed by a brief explanation on the next line.

Format:
0, 1, 2, 5, 7
Brief explanation of why these videos were selected.

Select exactly ${count} video indices (0-${filteredVideos.length - 1}). Prioritize relevance and quality.`

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI selection timeout')), 15000) // 15 second timeout
    })

    const result = await Promise.race([
      model.generateContent(systemPrompt),
      timeoutPromise
    ]) as any
    const responseText = result.response.text()

    try {
      console.log('🤖 AI Response:', responseText.substring(0, 200) + '...')

      // Parse simple text-based response format
      const lines = responseText.trim().split('\n').filter(line => line.trim())

      if (lines.length === 0) {
        throw new Error('Empty AI response')
      }

      // Extract indices from first line (comma-separated numbers)
      const firstLine = lines[0].trim()
      let selectedIndices: number[] = []

      // Try multiple parsing strategies for maximum reliability

      // Strategy 1: Direct comma-separated parsing
      try {
        // Remove common prefixes like "Selected:", "Indices:", etc.
        const cleanLine = firstLine.replace(/^(selected|indices|chosen|picked):\s*/i, '')
        selectedIndices = cleanLine
          .split(',')
          .map(s => s.trim())
          .filter(s => s.length > 0)
          .map(s => parseInt(s, 10))
          .filter(n => !isNaN(n) && n >= 0 && n < filteredVideos.length)
      } catch (e) {
        console.log('Strategy 1 failed, trying strategy 2')
      }

      // Strategy 2: Extract numbers from text (handles extra text)
      if (selectedIndices.length === 0) {
        try {
          const numberMatches = firstLine.match(/\d+/g)
          if (numberMatches) {
            selectedIndices = numberMatches
              .map(s => parseInt(s, 10))
              .filter(n => !isNaN(n) && n >= 0 && n < filteredVideos.length)
              .slice(0, count) // Prevent too many indices
          }
        } catch (e) {
          console.log('Strategy 2 failed, trying strategy 3')
        }
      }

      // Strategy 3: Look for JSON-like patterns (fallback for old format)
      if (selectedIndices.length === 0) {
        try {
          // Look for array patterns like [1, 2, 3] or [1,2,3]
          const jsonMatch = responseText.match(/\[[\d,\s]+\]/)
          if (jsonMatch) {
            const arrayStr = jsonMatch[0]
            const parsed = JSON.parse(arrayStr)
            if (Array.isArray(parsed)) {
              selectedIndices = parsed
                .filter((n: any) => typeof n === 'number' && n >= 0 && n < filteredVideos.length)
            }
          }
        } catch (e) {
          console.log('Strategy 3 failed')
        }
      }

      if (selectedIndices.length === 0) {
        throw new Error('No valid video indices found in AI response')
      }

      // Limit to requested count
      selectedIndices = selectedIndices.slice(0, count)

      // Get selected videos
      const selectedVideos = selectedIndices.map(index => filteredVideos[index])

      // Extract explanation from remaining lines or use default
      let explanation = `Selected ${selectedVideos.length} best videos for your request.`
      if (lines.length > 1) {
        explanation = lines.slice(1).join(' ').trim()
        // Clean up explanation (remove common AI artifacts)
        explanation = explanation
          .replace(/^(explanation|reason|because):\s*/i, '')
          .replace(/^these videos were selected/i, 'Selected')
      }

      console.log('✅ Parsed indices:', selectedIndices)
      console.log('✅ Explanation:', explanation.substring(0, 100) + '...')

      return {
        videos: selectedVideos,
        explanation
      }
    } catch (parseError) {
      console.error('❌ Failed to parse video selection:', parseError)
      console.error('❌ Raw AI response:', responseText)

      // Smart fallback: score videos by relevance + popularity
      const fallbackVideos = filteredVideos
        .map(video => ({
          video,
          score: this.calculateVideoScore(video, prompt)
        }))
        .sort((a, b) => b.score - a.score)
        .slice(0, count)
        .map(item => item.video)

      return {
        videos: fallbackVideos,
        explanation: `AI selection failed, showing ${count} best-matching videos by relevance and popularity.`
      }
    }
  }

  /**
   * Parse YouTube duration to minutes
   */
  private parseDurationToMinutes(duration: string): number {
    // Parse ISO 8601 duration format (PT1H2M3S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return 0

    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')

    return hours * 60 + minutes + seconds / 60
  }

  /**
   * Format view count for AI processing (simplified)
   */
  private formatViewCount(viewCount: number): string {
    if (viewCount >= 1000000) {
      return `${Math.round(viewCount / 1000000)}M`
    } else if (viewCount >= 1000) {
      return `${Math.round(viewCount / 1000)}K`
    }
    return viewCount.toString()
  }

  /**
   * Calculate video relevance score for fallback selection
   */
  private calculateVideoScore(video: VideoSearchResult, prompt: string): number {
    const promptLower = prompt.toLowerCase()
    const titleLower = video.title.toLowerCase()
    const descLower = video.description.toLowerCase()

    let score = 0

    // Title relevance (highest weight)
    const titleWords = promptLower.split(' ').filter(word => word.length > 2)
    titleWords.forEach(word => {
      if (titleLower.includes(word)) score += 10
    })

    // Description relevance
    titleWords.forEach(word => {
      if (descLower.includes(word)) score += 3
    })

    // View count bonus (normalized)
    const viewScore = Math.log10((video.viewCount || 1) + 1)
    score += viewScore

    return score
  }

  /**
   * Generate video recommendations based on user prompt
   */
  async generateVideoRecommendations(request: MagicQueueRequest): Promise<MagicQueueResponse> {
    const { prompt, count = 10, maxDuration, onProgress } = request

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      // Step 1: Generate search queries using AI
      console.log('🔍 Step 1: Generating search queries...')
      onProgress?.('Generating search queries...', 0, 3)
      const searchQueries = await this.generateSearchQueries(prompt, count)
      console.log('✅ Generated search queries:', searchQueries)
      onProgress?.('Generated search queries', 1, 3)

      // Step 2: Search YouTube for each query (in parallel for speed)
      console.log('🔍 Step 2: Searching YouTube...')
      onProgress?.('Searching YouTube...', 1, 3)

      const searchPromises = searchQueries.map(async (query, index) => {
        try {
          console.log(`  Searching for: "${query}"`)
          // OPTIMIZATION: Reduce search results multiplier to save quota
          // Changed from 1.5x to 1.2x to reduce API calls while still having selection options
          const results = await youtubeService.searchVideos(query, Math.ceil(count * 1.2))
          onProgress?.(`Searched ${index + 1}/${searchQueries.length} queries`, 1 + (index + 1) / searchQueries.length * 0.8, 3)
          return results
        } catch (error) {
          console.warn(`Failed to search for "${query}":`, error)

          // Check if it's a 403 error (API quota/key issue)
          if (error instanceof Error && error.message.includes('403')) {
            console.error('🚨 YouTube API 403 Error - Possible causes:')
            console.error('   • API quota exceeded (daily limit reached)')
            console.error('   • Invalid API key or key restrictions')
            console.error('   • YouTube Data API v3 not enabled')
            console.error('   • Check your Google Cloud Console settings')
          }

          return []
        }
      })

      const searchResults = await Promise.all(searchPromises)
      const allVideos = searchResults.flat()

      // Remove duplicates by video ID
      const uniqueVideos = allVideos.filter((video, index, self) =>
        self.findIndex(v => v.id === video.id) === index
      )

      console.log(`✅ Found ${uniqueVideos.length} unique videos from ${allVideos.length} total results`)
      onProgress?.('Found videos, selecting best ones...', 2, 3)

      if (uniqueVideos.length === 0) {
        throw new Error('No videos found for your request. Try a different prompt.')
      }

      // Step 3: Use AI to select the best videos
      console.log('🤖 Step 3: AI selecting best videos...')
      onProgress?.('AI analyzing videos...', 2.5, 3)
      const selection = await this.selectBestVideos(prompt, uniqueVideos, count, maxDuration)

      console.log(`✅ Selected ${selection.videos.length} best videos`)
      onProgress?.('Complete!', 3, 3)

      return {
        videos: selection.videos,
        explanation: selection.explanation,
        searchQueries
      }

    } catch (error) {
      console.error('❌ Error generating video recommendations:', error)

      if (error instanceof Error) {
        throw error
      }

      throw new Error('Failed to generate video recommendations. Please try again.')
    }
  }


}

// Export singleton instance
export const aiService = new AIService()
